import React from 'react';
import ServiceCard from './ServiceCard';
import { services } from '../data/services';

const Services: React.FC = () => {
  return (
    <section id="services" className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Our Professional{' '}
            <span className="gradient-nova bg-clip-text text-transparent">
              Services
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Comprehensive telemarketing and lead generation solutions designed to accelerate 
            your business growth and enhance customer relationships.
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <div 
              key={service.id} 
              className="animate-fade-in-up"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <ServiceCard service={service} />
            </div>
          ))}
        </div>

        {/* Bottom CTA Section */}
        <div className="mt-16 text-center">
          <div className="bg-white rounded-2xl shadow-xl p-8 md:p-12 gradient-nova-subtle">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">
              Ready to Transform Your Sales Process?
            </h3>
            <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
              Let our expert team help you generate more leads, increase conversions, 
              and build stronger customer relationships.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button 
                onClick={() => {
                  const element = document.getElementById('contact');
                  if (element) element.scrollIntoView({ behavior: 'smooth' });
                }}
                className="bg-nova-red text-white px-8 py-4 rounded-lg hover:bg-opacity-90 transition-all duration-200 font-semibold shadow-lg hover:shadow-xl"
              >
                Get Free Consultation
              </button>
              <button className="border-2 border-nova-red text-nova-red px-8 py-4 rounded-lg hover:bg-nova-red hover:text-white transition-all duration-200 font-semibold">
                View Case Studies
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Services;
