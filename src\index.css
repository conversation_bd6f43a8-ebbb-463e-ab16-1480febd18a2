@import "tailwindcss";

/* Novalead Brand Colors */
:root {
  --nova-red: #E94B6A;
  --nova-orange: #F29C2B;
  --nova-blue: #6EC1C9;
  --nova-purple: #7B5CA2;
  --nova-gray: #A39E9B;
}

/* Custom utility classes */
.text-nova-red { color: var(--nova-red); }
.text-nova-orange { color: var(--nova-orange); }
.text-nova-blue { color: var(--nova-blue); }
.text-nova-purple { color: var(--nova-purple); }
.text-nova-gray { color: var(--nova-gray); }

.bg-nova-red { background-color: var(--nova-red); }
.bg-nova-orange { background-color: var(--nova-orange); }
.bg-nova-blue { background-color: var(--nova-blue); }
.bg-nova-purple { background-color: var(--nova-purple); }
.bg-nova-gray { background-color: var(--nova-gray); }

.border-nova-red { border-color: var(--nova-red); }
.border-nova-orange { border-color: var(--nova-orange); }
.border-nova-blue { border-color: var(--nova-blue); }
.border-nova-purple { border-color: var(--nova-purple); }
.border-nova-gray { border-color: var(--nova-gray); }

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* Gradient backgrounds */
.gradient-nova {
  background: linear-gradient(135deg, var(--nova-red) 0%, var(--nova-orange) 25%, var(--nova-blue) 50%, var(--nova-purple) 100%);
}

.gradient-nova-subtle {
  background: linear-gradient(135deg, rgba(233, 75, 106, 0.1) 0%, rgba(242, 156, 43, 0.1) 25%, rgba(110, 193, 201, 0.1) 50%, rgba(123, 92, 162, 0.1) 100%);
}
